<template>
  <div class="signup-container">
    <div class="signup-header">
      <h2>培训报名</h2>
      <p v-if="trainingOrder">{{ trainingOrder.orderTitle }}</p>
    </div>

    <!-- 培训信息展示 -->
    <el-card v-if="trainingOrder" class="training-info-card" shadow="never">
      <template #header>
        <span>培训信息</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">培训类型：</span>
            <span class="value">{{ trainingOrder.trainingType }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">培训级别：</span>
            <span class="value">{{ trainingOrder.trainingLevel }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">培训时间：</span>
            <span class="value">{{ formatDateTime(trainingOrder.startDate) }} 至 {{ formatDateTime(trainingOrder.endDate)
              }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">培训地址：</span>
            <span class="value">{{ trainingOrder.trainingAddress }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">培训费用：</span>
            <span class="value price">{{ trainingOrder.trainingFee ? '￥' + trainingOrder.trainingFee : '免费' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">报名人数：</span>
            <span class="value">{{ trainingOrder.currentParticipants || 0 }}/{{ trainingOrder.maxParticipants || 0
              }}人</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 报名状态检查 -->
    <el-card v-if="applicationStatus" class="status-card" shadow="never">
      <template #header>
        <span>报名状态</span>
      </template>
      <el-alert :title="getStatusMessage()" :type="getStatusType()" :closable="false" show-icon>
      </el-alert>
      <div v-if="applicationStatus.applicationStatus === '1'" class="status-actions">
        <el-button type="danger" @click="handleCancelApplication">取消报名</el-button>
      </div>
    </el-card>

    <!-- 报名表单 -->
    <el-card v-if="!applicationStatus || applicationStatus.applicationStatus === '3'" class="signup-form-card"
      shadow="never">
      <template #header>
        <span>报名信息</span>
      </template>
      <el-form ref="signupFormRef" :model="signupForm" :rules="signupRules" label-width="120px"
        v-loading="submitLoading">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="applicantName">
              <el-input v-model="signupForm.applicantName" placeholder="请输入您的姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="applicantPhone">
              <el-input v-model="signupForm.applicantPhone" placeholder="请输入您的手机号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="applicantEmail">
              <el-input v-model="signupForm.applicantEmail" placeholder="请输入您的邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="applicantGender">
              <el-select v-model="signupForm.applicantGender" placeholder="请选择性别" style="width: 100%">
                <el-option label="男" value="男"></el-option>
                <el-option label="女" value="女"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="年龄" prop="applicantAge">
              <el-input-number v-model="signupForm.applicantAge" :min="16" :max="100" placeholder="请输入年龄"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历" prop="applicantEducation">
              <el-select v-model="signupForm.applicantEducation" placeholder="请选择学历" style="width: 100%">
                <el-option label="小学" value="小学"></el-option>
                <el-option label="初中" value="初中"></el-option>
                <el-option label="中专" value="中专"></el-option>
                <el-option label="高中" value="高中"></el-option>
                <el-option label="大专" value="大专"></el-option>
                <el-option label="本科" value="本科"></el-option>
                <el-option label="硕士" value="硕士"></el-option>
                <el-option label="博士" value="博士"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="身份证号" prop="applicantIdCard">
              <el-input v-model="signupForm.applicantIdCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="联系地址" prop="applicantAddress">
              <el-input v-model="signupForm.applicantAddress" placeholder="请输入联系地址" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="工作经验" prop="applicantExperience">
              <el-input v-model="signupForm.applicantExperience" type="textarea" :rows="3" placeholder="请简要描述您的工作经验" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="报名备注" prop="applicationNote">
              <el-input v-model="signupForm.applicationNote" type="textarea" :rows="3" placeholder="请输入报名备注（选填）" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" style="text-align: center;">
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading" size="large">
              提交报名
            </el-button>
            <el-button @click="handleCancel" size="large">
              取消
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTrainingOrder } from '@/api/training/order'
import { submitTrainingApplication, checkApplicationStatus, cancelMyApplication } from '@/api/training/application'

const route = useRoute()
const router = useRouter()

// 响应式数据
const trainingOrder = ref(null)
const applicationStatus = ref(null)
const submitLoading = ref(false)
const signupFormRef = ref(null)

// 报名表单数据
const signupForm = reactive({
  orderId: null,
  applicantName: '',
  applicantPhone: '',
  applicantEmail: '',
  applicantIdCard: '',
  applicantGender: '',
  applicantAge: null,
  applicantEducation: '',
  applicantExperience: '',
  applicantAddress: '',
  applicationNote: ''
})

// 表单验证规则
const signupRules = {
  applicantName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  applicantPhone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  applicantEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  applicantIdCard: [
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  const orderId = route.query.orderId
  if (orderId) {
    signupForm.orderId = parseInt(orderId)
    loadTrainingOrder(orderId)
    checkUserApplicationStatus(orderId)
  } else {
    ElMessage.error('缺少培训订单参数')
    router.back()
  }
})

// 方法
const loadTrainingOrder = async (orderId) => {
  try {
    const response = await getTrainingOrder(orderId)
    trainingOrder.value = response.data
  } catch (error) {
    ElMessage.error('获取培训信息失败')
    console.error(error)
  }
}

const checkUserApplicationStatus = async (orderId) => {
  try {
    const response = await checkApplicationStatus(orderId, null, null)
    if (response.data) {
      applicationStatus.value = response.data
    }
  } catch (error) {
    console.error('检查报名状态失败:', error)
  }
}

const handleSubmit = () => {
  if (!signupFormRef.value) return

  signupFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true

      try {
        await submitTrainingApplication(signupForm)
        ElMessage.success('报名成功，请等待审核')

        // 重新检查报名状态
        await checkUserApplicationStatus(signupForm.orderId)
      } catch (error) {
        ElMessage.error(error.msg || '报名失败，请稍后重试')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const handleCancel = () => {
  router.back()
}

const handleCancelApplication = async () => {
  try {
    await ElMessageBox.confirm('确认要取消报名吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await cancelMyApplication(applicationStatus.value.applicationId)
    ElMessage.success('取消报名成功')

    // 重新检查报名状态
    applicationStatus.value = null
    await checkUserApplicationStatus(signupForm.orderId)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || '取消报名失败')
    }
  }
}

const getStatusMessage = () => {
  if (!applicationStatus.value) return ''

  const statusMap = {
    '0': '您的报名申请已提交，请等待审核',
    '1': '恭喜！您的报名申请已通过审核',
    '2': '很抱歉，您的报名申请未通过审核',
    '3': '您已取消报名'
  }

  return statusMap[applicationStatus.value.applicationStatus] || '未知状态'
}

const getStatusType = () => {
  if (!applicationStatus.value) return 'info'

  const typeMap = {
    '0': 'warning',
    '1': 'success',
    '2': 'error',
    '3': 'info'
  }

  return typeMap[applicationStatus.value.applicationStatus] || 'info'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.signup-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.signup-header {
  text-align: center;
  margin-bottom: 20px;

  h2 {
    color: #303133;
    margin-bottom: 10px;
  }

  p {
    color: #606266;
    font-size: 16px;
  }
}

.training-info-card,
.status-card,
.signup-form-card {
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 10px;

  .label {
    font-weight: bold;
    color: #606266;
  }

  .value {
    color: #303133;

    &.price {
      color: #f56c6c;
      font-weight: bold;
    }
  }
}

.status-actions {
  margin-top: 15px;
  text-align: center;
}
</style>
