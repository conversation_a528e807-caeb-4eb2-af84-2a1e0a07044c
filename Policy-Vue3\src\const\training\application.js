/**
 * 培训报名表格配置
 */
export function createTrainingApplicationTableOption(proxy) {
  return {
    // 表格列配置
    column: [
      {
        label: '报名ID',
        prop: 'applicationId',
        width: 80,
        align: 'center',
        search: false,
        form: false,
        detail: true
      },
      {
        label: '培训订单',
        prop: 'orderTitle',
        minWidth: 200,
        search: false,
        form: false,
        detail: true,
        overHidden: true
      },
      {
        label: '培训订单ID',
        prop: 'orderId',
        width: 120,
        align: 'center',
        search: true,
        form: true,
        detail: false,
        type: 'select',
        dicUrl: '/training/order/list',
        dicMethod: 'get',
        dicQuery: { orderStatus: '1' },
        props: {
          label: 'orderTitle',
          value: 'orderId'
        },
        rules: [
          { required: true, message: '请选择培训订单', trigger: 'change' }
        ]
      },
      {
        label: '报名人姓名',
        prop: 'applicantName',
        minWidth: 120,
        search: true,
        form: true,
        detail: true,
        rules: [
          { required: true, message: '请输入报名人姓名', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      },
      {
        label: '报名人手机号',
        prop: 'applicantPhone',
        width: 130,
        search: true,
        form: true,
        detail: true,
        rules: [
          { required: true, message: '请输入报名人手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      {
        label: '报名人邮箱',
        prop: 'applicantEmail',
        minWidth: 180,
        search: false,
        form: true,
        detail: true,
        rules: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      {
        label: '身份证号',
        prop: 'applicantIdCard',
        width: 180,
        search: false,
        form: true,
        detail: true,
        rules: [
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
        ]
      },
      {
        label: '性别',
        prop: 'applicantGender',
        width: 80,
        align: 'center',
        search: false,
        form: true,
        detail: true,
        type: 'select',
        dicData: [
          { label: '男', value: '男' },
          { label: '女', value: '女' }
        ]
      },
      {
        label: '年龄',
        prop: 'applicantAge',
        width: 80,
        align: 'center',
        search: false,
        form: true,
        detail: true,
        type: 'number',
        min: 16,
        max: 100
      },
      {
        label: '学历',
        prop: 'applicantEducation',
        width: 100,
        align: 'center',
        search: false,
        form: true,
        detail: true,
        type: 'select',
        dicData: [
          { label: '小学', value: '小学' },
          { label: '初中', value: '初中' },
          { label: '中专', value: '中专' },
          { label: '高中', value: '高中' },
          { label: '大专', value: '大专' },
          { label: '本科', value: '本科' },
          { label: '硕士', value: '硕士' },
          { label: '博士', value: '博士' }
        ]
      },
      {
        label: '工作经验',
        prop: 'applicantExperience',
        minWidth: 200,
        search: false,
        form: true,
        detail: true,
        type: 'textarea',
        span: 24,
        overHidden: true
      },
      {
        label: '联系地址',
        prop: 'applicantAddress',
        minWidth: 200,
        search: false,
        form: true,
        detail: true,
        overHidden: true
      },
      {
        label: '报名状态',
        prop: 'applicationStatus',
        width: 100,
        align: 'center',
        search: true,
        form: false,
        detail: true,
        type: 'select',
        dicData: [
          { label: '待审核', value: '0' },
          { label: '已通过', value: '1' },
          { label: '已拒绝', value: '2' },
          { label: '已取消', value: '3' }
        ],
        slot: true
      },
      {
        label: '报名时间',
        prop: 'applicationTime',
        width: 160,
        align: 'center',
        search: false,
        form: false,
        detail: true,
        type: 'datetime',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        slot: true
      },
      {
        label: '审核人',
        prop: 'reviewer',
        width: 100,
        align: 'center',
        search: true,
        form: false,
        detail: true
      },
      {
        label: '审核时间',
        prop: 'reviewTime',
        width: 160,
        align: 'center',
        search: false,
        form: false,
        detail: true,
        type: 'datetime',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        slot: true
      },
      {
        label: '审核意见',
        prop: 'reviewComment',
        minWidth: 200,
        search: false,
        form: false,
        detail: true,
        type: 'textarea',
        span: 24,
        overHidden: true
      },
      {
        label: '报名备注',
        prop: 'applicationNote',
        minWidth: 200,
        search: false,
        form: true,
        detail: true,
        type: 'textarea',
        span: 24,
        overHidden: true
      },
      {
        label: '创建时间',
        prop: 'createTime',
        width: 160,
        align: 'center',
        search: true,
        form: false,
        detail: true,
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      }
    ],
    
    // 表单配置
    formOption: {
      submitBtn: true,
      emptyBtn: true,
      labelWidth: 120,
      gutter: 20,
      column: 2,
      menuPosition: 'center',
      detail: {
        labelWidth: 120,
        column: 2,
        gutter: 20
      }
    },
    
    // 搜索配置
    searchMenuSpan: 6,
    
    // 分页配置
    page: true,
    
    // 选择配置
    selection: true,
    
    // 索引配置
    index: true,
    indexLabel: '序号',
    
    // 菜单配置
    menu: true,
    menuWidth: 250,
    menuAlign: 'center',
    
    // 高度配置
    height: 'auto',
    calcHeight: 350,
    
    // 其他配置
    border: true,
    stripe: true,
    menuBtn: true,
    searchBtn: true,
    searchSpan: 4,
    
    // 权限配置
    addBtn: true,
    editBtn: true,
    delBtn: true,
    viewBtn: true
  }
}
