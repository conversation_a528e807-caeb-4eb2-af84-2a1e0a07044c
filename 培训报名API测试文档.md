# 培训报名API测试文档

## 测试环境

- 后端地址：http://localhost:8080
- 前端地址：http://localhost:80

## 公开API测试（无需登录）

### 1. 提交报名申请

**接口地址：** `POST /public/training/application/submit`

**请求示例：**
```json
{
  "orderId": 1,
  "applicantName": "张三",
  "applicantPhone": "13800138001",
  "applicantEmail": "<EMAIL>",
  "applicantIdCard": "110101199001011234",
  "applicantGender": "男",
  "applicantAge": 25,
  "applicantEducation": "本科",
  "applicantExperience": "有3年Java开发经验",
  "applicantAddress": "北京市朝阳区",
  "applicationNote": "希望能参加此次培训"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "报名成功，请等待审核",
  "data": null
}
```

### 2. 检查报名状态

**接口地址：** `GET /public/training/application/check-status`

**请求参数：**
- orderId: 培训订单ID（必填）
- phone: 手机号（可选）
- userId: 用户ID（可选）

**请求示例：**
```
GET /public/training/application/check-status?orderId=1&phone=13800138001
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "已报名",
  "data": {
    "applicationId": 1,
    "orderId": 1,
    "applicantName": "张三",
    "applicantPhone": "13800138001",
    "applicationStatus": "0",
    "applicationTime": "2025-07-23 10:00:00"
  }
}
```

### 3. 获取报名统计

**接口地址：** `GET /public/training/application/statistics/{orderId}`

**请求示例：**
```
GET /public/training/application/statistics/1
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalCount": 10,
    "pendingCount": 3,
    "approvedCount": 6,
    "rejectedCount": 1,
    "cancelledCount": 0
  }
}
```

## 管理API测试（需要登录）

### 1. 获取报名列表

**接口地址：** `GET /training/application/list`

**请求参数：**
- pageNum: 页码（默认1）
- pageSize: 每页条数（默认10）
- orderId: 培训订单ID（可选）
- applicantName: 报名人姓名（可选，模糊查询）
- applicantPhone: 手机号（可选）
- applicationStatus: 报名状态（可选）

**请求示例：**
```
GET /training/application/list?pageNum=1&pageSize=10&applicationStatus=0
```

**响应示例：**
```json
{
  "total": 50,
  "rows": [
    {
      "applicationId": 1,
      "orderId": 1,
      "orderTitle": "Java高级开发培训",
      "applicantName": "张三",
      "applicantPhone": "13800138001",
      "applicationStatus": "0",
      "applicationTime": "2025-07-23 10:00:00"
    }
  ],
  "code": 200,
  "msg": "查询成功"
}
```

### 2. 审核报名

**接口地址：** `PUT /training/application/review/{applicationId}`

**请求参数：**
- status: 审核状态（1-通过，2-拒绝）
- reviewComment: 审核意见（可选）

**请求示例：**
```
PUT /training/application/review/1?status=1&reviewComment=符合报名条件，通过审核
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "审核成功",
  "data": 1
}
```

### 3. 批量审核

**接口地址：** `PUT /training/application/batch-review`

**请求参数：**
- applicationIds: 报名ID数组
- status: 审核状态
- reviewComment: 审核意见

**请求示例：**
```
PUT /training/application/batch-review?applicationIds=1,2,3&status=1&reviewComment=批量通过
```

## 测试用例

### 测试用例1：正常报名流程

1. **提交报名申请**
   - 使用有效的培训订单ID
   - 填写完整的报名信息
   - 验证返回成功消息

2. **检查报名状态**
   - 使用相同的订单ID和手机号
   - 验证返回报名记录
   - 状态应为"0"（待审核）

3. **管理员审核**
   - 登录管理员账号
   - 查看报名列表
   - 审核通过报名申请

4. **再次检查状态**
   - 验证状态变为"1"（已通过）

### 测试用例2：重复报名检查

1. **首次报名**
   - 提交报名申请
   - 验证成功

2. **重复报名**
   - 使用相同手机号再次报名
   - 验证返回错误消息

### 测试用例3：报名人数限制

1. **查看培训订单**
   - 确认最大报名人数

2. **报名至满员**
   - 提交报名直到达到上限

3. **超额报名**
   - 再次提交报名
   - 验证返回"报名人数已满"错误

### 测试用例4：报名截止时间

1. **正常时间报名**
   - 在截止时间前报名
   - 验证成功

2. **超时报名**
   - 修改系统时间或培训订单截止时间
   - 尝试报名
   - 验证返回"报名已截止"错误

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录过期 |
| 403 | 无权限访问 |
| 500 | 服务器内部错误 |

## 常见错误及解决方案

### 1. 培训订单不存在
**错误信息：** "培训订单不存在"
**解决方案：** 检查orderId是否正确，确认培训订单已发布

### 2. 重复报名
**错误信息：** "您已报名此培训，请勿重复报名"
**解决方案：** 检查是否已经报名，可以查询报名状态

### 3. 报名已截止
**错误信息：** "报名已截止"
**解决方案：** 检查培训订单的报名截止时间

### 4. 报名人数已满
**错误信息：** "报名人数已满"
**解决方案：** 等待其他人取消报名或选择其他培训

### 5. 权限不足
**错误信息：** "权限不足"
**解决方案：** 确认用户角色和权限配置

## 性能测试建议

1. **并发报名测试**
   - 模拟多用户同时报名
   - 验证数据一致性

2. **大数据量测试**
   - 创建大量报名记录
   - 测试查询和分页性能

3. **接口响应时间测试**
   - 测试各接口的响应时间
   - 确保在可接受范围内

## 安全测试建议

1. **SQL注入测试**
   - 在参数中注入SQL语句
   - 验证系统防护能力

2. **XSS攻击测试**
   - 在文本字段中注入脚本
   - 验证输入过滤机制

3. **权限绕过测试**
   - 尝试访问无权限的接口
   - 验证权限控制有效性
