<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>数据加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .job-card {
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background: #fff;
        }
        .job-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .job-info {
            display: flex;
            gap: 15px;
            margin-bottom: 8px;
        }
        .job-info span {
            padding: 2px 6px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        .job-salary {
            font-weight: bold;
            color: #ff6000;
        }
        button {
            padding: 8px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>招聘信息数据加载测试</h1>
    
    <div class="test-section">
        <h3>1. 测试模拟数据加载</h3>
        <button onclick="testMockData()">加载模拟数据</button>
        <button onclick="clearData()">清空数据</button>
        <div id="mockDataResult"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试API数据加载</h3>
        <button onclick="testApiData()">测试API调用</button>
        <div id="apiDataResult"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试KnockoutJS绑定</h3>
        <button onclick="testKnockoutBinding()">测试数据绑定</button>
        <div id="knockoutTest">
            <p>招聘信息数量: <span data-bind="text: jobPostings().length"></span></p>
            <div data-bind="foreach: jobPostings">
                <div class="job-card">
                    <div class="job-title" data-bind="text: jobTitle"></div>
                    <div class="job-info">
                        <span data-bind="text: jobType"></span>
                        <span data-bind="text: workLocation"></span>
                        <span data-bind="text: jobCategory"></span>
                    </div>
                    <div class="job-salary" data-bind="text: salaryRange"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h3>4. 调试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="debugLog" class="log"></div>
    </div>

    <script src="../public/js/jquery-3.5.0.min.js"></script>
    <script src="../public/js/knockout.js"></script>
    <script>
        // 创建测试用的ViewModel
        var testViewModel = {
            jobPostings: ko.observableArray([])
        };
        
        // 绑定到KnockoutJS
        ko.applyBindings(testViewModel, document.getElementById('knockoutTest'));
        
        // 日志函数
        function log(message) {
            var logDiv = document.getElementById('debugLog');
            var timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += '[' + timestamp + '] ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('debugLog').textContent = '';
        }
        
        // 测试模拟数据
        function testMockData() {
            log('开始测试模拟数据加载...');
            
            var mockJobs = [
                {
                    jobId: 1,
                    jobTitle: 'Java高级开发工程师',
                    jobType: '全职',
                    jobCategory: 'IT技术',
                    workLocation: '青岛市市南区',
                    salaryRange: '￥8000-15000/月',
                    viewCount: 156
                },
                {
                    jobId: 2,
                    jobTitle: '前端开发工程师',
                    jobType: '全职',
                    jobCategory: 'IT技术',
                    workLocation: '青岛市崂山区',
                    salaryRange: '￥6000-12000/月',
                    viewCount: 89
                },
                {
                    jobId: 3,
                    jobTitle: '餐厅服务员',
                    jobType: '兼职',
                    jobCategory: '餐饮服务',
                    workLocation: '青岛市市北区',
                    salaryRange: '￥20-25/小时',
                    viewCount: 234
                }
            ];
            
            testViewModel.jobPostings(mockJobs);
            log('模拟数据加载完成，数量: ' + mockJobs.length);
            
            // 显示结果
            var resultDiv = document.getElementById('mockDataResult');
            var html = '<h4>加载的模拟数据:</h4>';
            mockJobs.forEach(function(job) {
                html += '<div class="job-card">';
                html += '<div class="job-title">' + job.jobTitle + '</div>';
                html += '<div class="job-info">';
                html += '<span>' + job.jobType + '</span>';
                html += '<span>' + job.workLocation + '</span>';
                html += '<span>' + job.jobCategory + '</span>';
                html += '</div>';
                html += '<div class="job-salary">' + job.salaryRange + '</div>';
                html += '</div>';
            });
            resultDiv.innerHTML = html;
        }
        
        // 清空数据
        function clearData() {
            testViewModel.jobPostings([]);
            document.getElementById('mockDataResult').innerHTML = '';
            document.getElementById('apiDataResult').innerHTML = '';
            log('数据已清空');
        }
        
        // 测试API调用
        function testApiData() {
            log('开始测试API数据加载...');
            
            var baseUrl = 'http://localhost:80/sux-admin/';
            var url = baseUrl + 'public/job/postings?pageNum=1&pageSize=5';
            
            log('请求URL: ' + url);
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.timeout = 10000;
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            log('API响应成功: ' + JSON.stringify(response, null, 2));
                            
                            var resultDiv = document.getElementById('apiDataResult');
                            resultDiv.innerHTML = '<h4>API响应:</h4><pre>' + JSON.stringify(response, null, 2) + '</pre>';
                            
                            if (response.code == 0 || response.code == 200) {
                                var jobList = response.rows || response.data || [];
                                if (jobList.length > 0) {
                                    testViewModel.jobPostings(jobList);
                                    log('API数据加载到ViewModel成功，数量: ' + jobList.length);
                                } else {
                                    log('API返回的数据为空');
                                }
                            } else {
                                log('API返回错误: ' + (response.msg || response.message));
                            }
                        } catch (e) {
                            log('解析API响应失败: ' + e.message);
                            document.getElementById('apiDataResult').innerHTML = '<h4>解析错误:</h4><p>' + e.message + '</p><pre>' + xhr.responseText + '</pre>';
                        }
                    } else {
                        log('API请求失败: ' + xhr.status + ' ' + xhr.statusText);
                        document.getElementById('apiDataResult').innerHTML = '<h4>请求失败:</h4><p>' + xhr.status + ' ' + xhr.statusText + '</p>';
                    }
                }
            };
            
            xhr.ontimeout = function () {
                log('API请求超时');
                document.getElementById('apiDataResult').innerHTML = '<h4>请求超时</h4>';
            };
            
            xhr.onerror = function () {
                log('API请求发生错误');
                document.getElementById('apiDataResult').innerHTML = '<h4>网络错误</h4>';
            };
            
            xhr.send();
        }
        
        // 测试KnockoutJS绑定
        function testKnockoutBinding() {
            log('测试KnockoutJS数据绑定...');
            
            // 添加一些测试数据
            var testData = [
                {
                    jobId: 999,
                    jobTitle: '测试职位 - KnockoutJS绑定',
                    jobType: '测试',
                    jobCategory: '测试分类',
                    workLocation: '测试地点',
                    salaryRange: '￥测试薪资'
                }
            ];
            
            testViewModel.jobPostings(testData);
            log('KnockoutJS绑定测试完成');
        }
        
        // 页面加载完成后的初始化
        $(document).ready(function() {
            log('页面加载完成，开始初始化...');
            clearLog();
            log('测试页面初始化完成');
        });
    </script>
</body>
</html>
