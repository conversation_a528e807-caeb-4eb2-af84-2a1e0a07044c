# 培训报名功能使用说明

## 功能概述

本次开发完成了完整的培训报名功能，包括前端报名页面、后台管理系统和数据库设计。用户可以从前端HTML页面跳转到后台进行报名，管理员可以在后台管理系统中查看和审核报名信息。

## 功能特性

### 1. 前端报名功能
- 用户可以从培训详情页面直接跳转到报名页面
- 支持填写详细的个人信息（姓名、手机号、邮箱、身份证、学历等）
- 实时检查报名状态，防止重复报名
- 支持查看培训信息和报名统计

### 2. 后台管理功能
- 报名列表查看和搜索
- 报名信息的增删改查
- 报名审核（通过/拒绝）
- 批量审核功能
- 报名数据导出
- 报名统计分析

### 3. 数据安全
- 防止重复报名（同一手机号或用户ID）
- 报名状态管理（待审核、已通过、已拒绝、已取消）
- 软删除机制，保证数据安全

## 部署步骤

### 1. 数据库部署

执行以下SQL文件创建数据库表：

```sql
-- 1. 创建培训报名表
source e:\policy/Policy-Springboot3/sux-admin/sql/training_application_table.sql

-- 2. 创建菜单权限
source e:\policy/Policy-Springboot3/sux-admin/sql/training_application_menu.sql
```

### 2. 后端部署

确保以下文件已正确部署：

**实体类：**
- `e:\policy/Policy-Springboot3/sux-system\src\main\java/com\sux/system\domain/TrainingApplication.java`

**Mapper接口：**
- `e:\policy/Policy-Springboot3/sux-system\src\main\java/com\sux/system\mapper/TrainingApplicationMapper.java`

**Mapper XML：**
- `e:\policy/Policy-Springboot3/sux-system\src\main\resources/mapper\training/TrainingApplicationMapper.xml`

**服务层：**
- `e:\policy/Policy-Springboot3/sux-system\src\main\java/com\sux/system\service/ITrainingApplicationService.java`
- `e:\policy/Policy-Springboot3/sux-system\src\main\java/com\sux/system\service/impl/TrainingApplicationServiceImpl.java`

**控制器：**
- `e:\policy/Policy-Springboot3/sux-admin/src\main\java\com/sux/web\controller/training/TrainingApplicationController.java`
- `e:\policy/Policy-Springboot3/sux-admin/src\main\java\com/sux/web\controller/training/PublicTrainingApplicationController.java`

### 3. 前端部署

确保以下文件已正确部署：

**页面组件：**
- `e:\policy/Policy-Vue3\src\views\zhaop\application\index.vue` - 后台管理页面
- `e:\policy/Policy-Vue3\src\views\zhaop\application\TrainingApplicationFormDialog.vue` - 表单组件
- `e:\policy/Policy-Vue3\src\views\zhaop\application\signup.vue` - 公开报名页面

**API接口：**
- `e:\policy/Policy-Vue3\src\api\training\application.js`

**配置文件：**
- `e:\policy/Policy-Vue3\src\const\training\application.js`

**路由配置：**
- 已在 `e:\policy/Policy-Vue3\src\router\index.js` 中添加公开报名页面路由

### 4. HTML页面修改

已修改 `e:\policy/web-site\web-html\jiuye\activity\trainingOrderDetail.html` 中的报名按钮，使其跳转到后台报名页面。

## 使用流程

### 用户报名流程

1. **访问培训详情页面**
   - 用户在前端网站查看培训详情
   - 点击"立即报名"按钮

2. **跳转到报名页面**
   - 系统自动跳转到后台报名页面
   - URL格式：`http://localhost:80/#/signup?orderId=培训订单ID`

3. **填写报名信息**
   - 填写个人基本信息（姓名、手机号等）
   - 填写教育背景和工作经验
   - 添加报名备注（可选）

4. **提交报名**
   - 系统验证信息完整性
   - 检查是否重复报名
   - 提交成功后等待审核

5. **查看报名状态**
   - 可以随时查看报名审核状态
   - 如果审核通过可以取消报名（培训开始前）

### 管理员审核流程

1. **登录后台管理系统**
   - 使用管理员账号登录

2. **进入报名管理页面**
   - 导航到"招聘管理" -> "培训报名管理"

3. **查看报名列表**
   - 查看所有报名记录
   - 可以按培训订单、报名状态等条件筛选

4. **审核报名**
   - 查看报名详情
   - 选择通过或拒绝
   - 填写审核意见

5. **批量操作**
   - 支持批量审核
   - 支持批量导出数据

## API接口说明

### 公开接口（无需登录）

- `POST /public/training/application/submit` - 提交报名申请
- `GET /public/training/application/check-status` - 检查报名状态
- `GET /public/training/application/statistics/{orderId}` - 获取报名统计

### 管理接口（需要登录和权限）

- `GET /training/application/list` - 获取报名列表
- `GET /training/application/{applicationId}` - 获取报名详情
- `POST /training/application` - 新增报名
- `PUT /training/application` - 修改报名
- `DELETE /training/application/{applicationIds}` - 删除报名
- `PUT /training/application/review/{applicationId}` - 审核报名
- `PUT /training/application/batch-review` - 批量审核

## 权限配置

系统已自动创建以下权限：

- `training:application:list` - 查看报名列表
- `training:application:query` - 查询报名详情
- `training:application:add` - 新增报名
- `training:application:edit` - 修改报名
- `training:application:remove` - 删除报名
- `training:application:export` - 导出报名数据
- `training:application:review` - 审核报名
- `training:application:cancel` - 取消报名

## 注意事项

1. **数据库表创建**：确保先执行数据库脚本创建相关表
2. **菜单权限**：执行菜单SQL后需要重新分配角色权限
3. **前端路由**：确保前端路由配置正确
4. **跨域配置**：如果前后端分离部署，注意跨域配置
5. **文件上传**：如需支持附件上传，需要额外配置

## 测试建议

1. **功能测试**
   - 测试报名提交流程
   - 测试重复报名检查
   - 测试审核流程
   - 测试取消报名功能

2. **权限测试**
   - 测试不同角色的权限控制
   - 测试公开接口的访问

3. **数据测试**
   - 测试大量数据的性能
   - 测试数据导出功能

## 扩展功能建议

1. **短信通知**：审核结果短信通知
2. **邮件提醒**：培训开始前邮件提醒
3. **在线支付**：支持在线缴费功能
4. **电子证书**：培训完成后生成电子证书
5. **评价系统**：培训结束后的评价反馈
